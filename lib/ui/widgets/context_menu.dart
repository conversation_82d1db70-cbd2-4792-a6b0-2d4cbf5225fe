import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
import 'package:noeji/models/enums.dart';
import 'package:noeji/services/auth/auth_providers.dart';
import 'package:noeji/services/subscription/subscription_providers.dart';
import 'package:noeji/ui/providers/color_filter_provider.dart';
import 'package:noeji/ui/providers/combined_filter_provider.dart';
import 'package:noeji/ui/providers/group_provider.dart';
import 'package:noeji/ui/providers/sort_provider.dart';
import 'package:noeji/ui/screens/settings_screen.dart';
import 'package:noeji/ui/screens/sign_in_screen.dart';
import 'package:noeji/ui/theme/noeji_theme.dart';
import 'package:noeji/utils/logger.dart';

// Import the menu state provider
import 'package:noeji/ui/screens/ideabooks_list_screen.dart' show isMenuOpenProvider;

/// Context menu for the app
class ContextMenu extends ConsumerWidget {
  /// Constructor
  const ContextMenu({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: 250,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border.all(
          color: NoejiTheme.colorsOf(context).border,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Sign in option (only show if user is not signed in)
          Consumer(
            builder: (context, ref, child) {
              final isSignedIn = ref.watch(isSignedInProvider);

              if (!isSignedIn) {
                // User is not signed in, show sign in option
                return Column(
                  children: [
                    ListTile(
                      leading: Icon(
                        Icons.person,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      title: Text(
                        'Sign in',
                        style: NoejiTheme.textStylesOf(context).bodyMedium,
                      ),
                      onTap: () {
                        // Reset menu state
                        ref.read(isMenuOpenProvider.notifier).state = false;

                        // Close the menu
                        Navigator.of(context).pop();

                        // Navigate to sign in screen
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const SignInScreen(),
                          ),
                        );
                      },
                    ),
                    // Divider after sign in
                    Divider(
                      height: 1,
                      thickness: 1,
                      color: NoejiTheme.colorsOf(context).divider,
                    ),
                  ],
                );
              } else {
                // User is signed in, don't show anything (no profile section)
                return const SizedBox.shrink();
              }
            },
          ),

          // Upgrade to Pro (only show if user is signed in and not already pro)
          Consumer(
            builder: (context, ref, child) {
              final authProcess = ref.watch(authProcessProvider);
              final isSignedIn = ref.watch(isSignedInProvider);
              final isProUser = ref.watch(realtimeIsProUserProvider);

              // If signing out, hide the upgrade option
              if (authProcess == AuthProcess.signingOut) {
                return const SizedBox.shrink();
              }

              if (isSignedIn && !isProUser) {
                return Column(
                  children: [
                    ListTile(
                      leading: Icon(
                        Icons.workspace_premium,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      title: Row(
                        children: [
                          Text(
                            'Upgrade to ',
                            style: NoejiTheme.textStylesOf(context).bodyMedium,
                          ),
                          SvgPicture.asset(
                            'assets/images/noeji_pro_logo.svg',
                            height: 20, // Match the approximate height of the original text
                            colorFilter: ColorFilter.mode(
                              NoejiTheme.colorsOf(context).textPrimary,
                              BlendMode.srcIn,
                            ),
                          ),
                        ],
                      ),
                      onTap: () async {
                        // Reset menu state
                        ref.read(isMenuOpenProvider.notifier).state = false;

                        // Close the menu
                        Navigator.of(context).pop();

                        try {
                          // Show the RevenueCat paywall
                          final result = await RevenueCatUI.presentPaywall();

                          // Handle the result
                          switch (result) {
                            case PaywallResult.purchased:
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Thank you for your purchase!',
                                    ),
                                  ),
                                );
                              }
                              break;
                            case PaywallResult.restored:
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Your purchase has been restored!',
                                    ),
                                  ),
                                );
                              }
                              break;
                            case PaywallResult.cancelled:
                              // User cancelled the purchase, no action needed
                              break;
                            case PaywallResult.error:
                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'There was an error processing your purchase.',
                                    ),
                                  ),
                                );
                              }
                              break;
                            case PaywallResult.notPresented:
                              Logger.error('Paywall was not presented');
                              break;
                          }
                        } catch (e) {
                          Logger.error('Error showing paywall', e);
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Error showing subscription options: ${e.toString()}',
                                ),
                              ),
                            );
                          }
                        }
                      },
                    ),
                    Divider(
                      height: 1,
                      thickness: 1,
                      color: NoejiTheme.colorsOf(context).divider,
                    ),
                  ],
                );
              } else {
                return const SizedBox.shrink();
              }
            },
          ),

          // Settings (only show if user is signed in)
          Consumer(
            builder: (context, ref, child) {
              final isSignedIn = ref.watch(isSignedInProvider);

              if (isSignedIn) {
                return Column(
                  children: [
                    ListTile(
                      leading: Icon(
                        Icons.settings,
                        color: Theme.of(context).iconTheme.color,
                      ),
                      title: Text(
                        'Settings',
                        style: NoejiTheme.textStylesOf(context).bodyMedium,
                      ),
                      onTap: () {
                        // Reset menu state
                        ref.read(isMenuOpenProvider.notifier).state = false;

                        // Close the menu
                        Navigator.of(context).pop();

                        // Navigate to settings screen
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const SettingsScreen(),
                          ),
                        );
                      },
                    ),
                    Divider(
                      height: 1,
                      thickness: 1,
                      color: NoejiTheme.colorsOf(context).divider,
                    ),
                  ],
                );
              } else {
                return const SizedBox.shrink();
              }
            },
          ),

          // Sort order
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Consumer(
                builder: (context, ref, child) {
                  final sortOrder = ref.watch(ideabookSortOrderProvider);
                  final isAscending = sortOrder == SortOrder.ascending;

                  return ListTile(
                    leading: Icon(
                      Icons.sort,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    title: Text(
                      'Sort order',
                      style: NoejiTheme.textStylesOf(context).bodyMedium,
                    ),
                    subtitle: Text(
                      isAscending ? 'Oldest first' : 'Newest first',
                      style: NoejiTheme.textStylesOf(context).bodySmall,
                    ),
                    trailing: Icon(
                      isAscending ? Icons.arrow_upward : Icons.arrow_downward,
                      color: Theme.of(context).iconTheme.color,
                      size: 18,
                    ),
                    onTap: () {
                      // Toggle sort order using the notifier method
                      Logger.debug(
                        'Toggling sort order from ${sortOrder.name}',
                      );
                      ref
                          .read(ideabookSortOrderProvider.notifier)
                          .toggleSortOrder();

                      // Force a rebuild of the ideabooks list
                      ref.invalidate(sortOrderLoggerProvider);

                      // Force a refresh of the combined filtered ideabooks provider
                      ref.invalidate(combinedFilteredIdeabooksProvider);

                      // Reset menu state
                      ref.read(isMenuOpenProvider.notifier).state = false;

                      // Close the menu
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),

              Divider(
                height: 1,
                thickness: 1,
                color: NoejiTheme.colorsOf(context).divider,
              ),
            ],
          ),

          // Group by color
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Consumer(
                builder: (context, ref, child) {
                  final isGroupByColorEnabled = ref.watch(groupByColorProvider);

                  return ListTile(
                    leading: Icon(
                      Icons.color_lens,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    title: Text(
                      'Group by color',
                      style: NoejiTheme.textStylesOf(context).bodyMedium,
                    ),
                    trailing: Icon(
                      isGroupByColorEnabled
                          ? Icons.check_box
                          : Icons.check_box_outline_blank,
                      color: Theme.of(context).iconTheme.color,
                      size: 18,
                    ),
                    onTap: () {
                      // Toggle group by color using the notifier method
                      Logger.debug(
                        'Toggling group by color from $isGroupByColorEnabled to ${!isGroupByColorEnabled}',
                      );
                      ref
                          .read(groupByColorProvider.notifier)
                          .toggleGroupByColor();

                      // Force a refresh of the combined filtered ideabooks provider
                      Logger.debug(
                        'Invalidating combinedFilteredIdeabooksProvider',
                      );
                      ref.invalidate(combinedFilteredIdeabooksProvider);

                      // Force a rebuild of the group by color logger
                      ref.invalidate(groupByColorLoggerProvider);

                      // Reset menu state
                      ref.read(isMenuOpenProvider.notifier).state = false;

                      // Close the menu
                      Navigator.of(context).pop();
                    },
                  );
                },
              ),

              Divider(
                height: 1,
                thickness: 1,
                color: NoejiTheme.colorsOf(context).divider,
              ),
            ],
          ),

          // Filter by color
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ListTile(
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                leading: Icon(
                  Icons.filter_list,
                  color: Theme.of(context).iconTheme.color,
                  size: 20,
                ),
                title: Text(
                  'Filter by color',
                  style: NoejiTheme.textStylesOf(context).bodyMedium,
                ),
              ),

              // Color options in a single row with equal spacing
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // Get all colors except 'none'
                    final colors =
                        IdeabookColor.values
                            .where((color) => color != IdeabookColor.none)
                            .toList();
                    final colorCount = colors.length;
                    final colorSize = 24.0;

                    // Calculate available width for spacing with increased spacing between colors
                    final availableWidth = constraints.maxWidth;
                    final totalColorWidth = colorCount * colorSize;
                    final totalSpacingWidth = availableWidth - totalColorWidth;

                    // Increase spacing between colors by reducing edge padding
                    // Use 95% of available space for between-color spacing, 5% for edge padding
                    final betweenColorSpacing =
                        (totalSpacingWidth * 0.95) / (colorCount - 1);
                    final edgeSpacing = (totalSpacingWidth * 0.05) / 2;

                    return Row(
                      children: [
                        // Add initial edge spacing
                        SizedBox(width: edgeSpacing),
                        // Add colors with spacing
                        for (int i = 0; i < colors.length; i++) ...[
                          GestureDetector(
                            onTap: () {
                              // Set color filter
                              ref.read(colorFilterProvider.notifier).state =
                                  colors[i];
                              // Reset menu state
                              ref.read(isMenuOpenProvider.notifier).state = false;
                              // Close the menu
                              Navigator.of(context).pop();
                            },
                            child: Container(
                              width: colorSize,
                              height: colorSize,
                              decoration: BoxDecoration(
                                color: NoejiTheme.getIdeabookColor(
                                  context,
                                  colors[i].index,
                                ),
                                border: Border.all(
                                  color: NoejiTheme.colorsOf(context).border,
                                  // Remove border by intention. Do not change back to 1.
                                  width: 0,
                                ),
                              ),
                            ),
                          ),
                          // Add spacing after each color except the last one
                          if (i < colors.length - 1)
                            SizedBox(width: betweenColorSpacing),
                        ],
                        // Add final edge spacing
                        SizedBox(width: edgeSpacing),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
